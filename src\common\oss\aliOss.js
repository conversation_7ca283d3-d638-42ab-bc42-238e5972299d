import Tool from '@/common/tool';
import { getOSSToken, uploadTaskMap } from './index';

// 模块加载状态管理
let OSS = null;
let isLoading = false;
let loadPromise = null;

// 内部加载函数
const ensureOSS = async () => {
    // 如果已经加载过，直接返回
    if (OSS) {
        return OSS;
    }

    // 如果正在加载中，返回已存在的 Promise
    if (isLoading && loadPromise) {
        return loadPromise;
    }

    // 开始新的加载
    isLoading = true;
    loadPromise = import(/* webpackPrefetch: true */ 'ali-oss').then(module => {
        OSS = module.default;
        isLoading = false;
        return OSS;
    }).catch(error => {
        isLoading = false;
        loadPromise = null;
        throw error;
    });

    return loadPromise;
};

// 处理分片上传
function handleMultipartUpload({ ossClient, file, filePath, progress, success, error }) {
    // 根据文件大小动态调整分片大小和缓存策略
    const fileSize = file.size;
    let partSize, cacheControl;

    if (fileSize > 100 * 1024 * 1024) { // 大于100MB
        partSize = 20 * 1024 * 1024; // 20MB分片
        cacheControl = 'no-cache, no-store, must-revalidate'; // 不缓存大文件分片
    } else if (fileSize > 10 * 1024 * 1024) { // 大于10MB
        partSize = 10 * 1024 * 1024; // 10MB分片
        cacheControl = 'public, max-age=3600'; // 缓存1小时
    } else {
        partSize = 5 * 1024 * 1024; // 5MB分片
        cacheControl = 'public, max-age=86400'; // 缓存24小时
    }

    return ossClient.multipartUpload(filePath, file, {
        parallel: 2,
        partSize: partSize,
        progress: (percentage, cpt) => {
            if (progress) {
                progress(percentage, cpt);
            }
        },
        headers: {
            'Cache-Control': cacheControl,
            'Content-Disposition': 'attachment',
            // 对于分片上传，使用通用二进制类型
            'Content-Type': 'application/octet-stream'
        }
    }).then(success).catch(error);
}

// 处理同桶拷贝
function handleCopy({ ossClient, copyOssPath, filePath, progress, success, error }) {
    const uploadId = Tool.genID(11); // 生成唯一的上传ID
    if (progress) {
        progress(0.1, { uploadId });
    } // 拷贝进度1%

    ossClient.copy(filePath, copyOssPath, {
        headers: {
            'Content-Disposition': 'attachment'
        }
    }).then(res => {
        if (progress) {
            progress(1, { uploadId });
        } // 拷贝完成，进度100%
        if (success) {
            success(res);
        }
    }).catch(err => {
        if (progress) {
            progress(0, { uploadId });
        } // 拷贝失败，进度重置为0
        if (error) {
            error(err);
        }
    });
}
// 处理文件上传或拷贝
function handleUploadOrCopy({ ossClient, file, filePath, copyOssPath, progress, success, error }) {
    if (copyOssPath) {
        return handleCopy({ ossClient, copyOssPath, filePath, progress, success, error });
    }
    return handleMultipartUpload({ ossClient, file, filePath, progress, success, error });
}
// 进度处理
function handleProgress({ percentage, cpt, callback, ossClient, uploadId }) {
    let progress = Math.round(percentage * 100)
    if(!uploadTaskMap.hasOwnProperty(uploadId)){
        uploadTaskMap[uploadId] = {}
    }
    if (cpt) {
        uploadTaskMap[uploadId].checkPoint = cpt
    }
    if(ossClient){
        uploadTaskMap[uploadId].ossClient =  ossClient
    }
    uploadTaskMap[uploadId].callback = callback
    uploadTaskMap[uploadId].percentage = percentage
    uploadTaskMap[uploadId].uploading = true
    uploadTaskMap[uploadId].lastActivity = Date.now() // 添加活跃时间跟踪
    console.log("progress",uploadId,progress)
    callback && callback("progress", progress,uploadId);
}

// 清理 OSS URL，移除 uploadId 参数
const cleanOssUrl = (url) => {
    if (!url) {
        return url;
    }
    try {
        // 创建 URL 对象来解析和清理参数
        const urlObj = new URL(url);
        // 移除 uploadId 参数
        urlObj.searchParams.delete('uploadId');
        // 返回清理后的 URL
        return urlObj.toString();
    } catch (error) {
        console.warn('Failed to parse OSS URL:', url, error);
        // 如果 URL 解析失败，尝试简单的字符串替换
        return url.replace(/[?&]uploadId=[^&]*(&|$)/, '$1').replace(/[?&]$/, '');
    }
};

// 上传成功处理
const handleUploadSuccess = (res, callback, uploadId) => {
    console.log("handleUploadSuccess",res,callback,uploadId)
    const originalUrl = res.res.requestUrls[0];
    // 清理 URL，移除 uploadId 参数
    const cleanUrl = cleanOssUrl(originalUrl);
    console.log('OSS URL cleaned:', originalUrl, '->', cleanUrl);

    if (callback) {
        callback("complete", cleanUrl);
    }
    if (uploadTaskMap[uploadId] && uploadTaskMap[uploadId].timer) {
        clearTimeout(uploadTaskMap[uploadId].timer);
        uploadTaskMap[uploadId].timer = null
    }
    delete uploadTaskMap[uploadId];
};

// 上传错误处理
const handleUploadError = ({ e, callback, uploadId }) => {
    console.log("[event] uploadFile -- error",e,uploadTaskMap,uploadId);
    if(!uploadTaskMap[uploadId]){
        return
    }
    if(e&&e.name==='timeout'){
        uploadTaskMap[uploadId].error = true
    }else if(uploadTaskMap[uploadId].pause){
        e.name = 'pause'
    }else{
        uploadTaskMap[uploadId].error = true
    }
    uploadTaskMap[uploadId].uploading = false

    if(uploadTaskMap[uploadId].timer){
        clearTimeout(uploadTaskMap[uploadId].timer)
        uploadTaskMap[uploadId].timer = null
        uploadTaskMap[uploadId].isTimeout = false
    }
    callback && callback("error", e, uploadId);
};
// 重新上传
const handleReUploadFile = ({ ossClient, file, filePath, checkPoint }, { progress, success, error }) => {
    ossClient.multipartUpload(filePath, file, {
        checkpoint: checkPoint,
        progress: (percentage, cpt) => {
            if (progress) {
                progress(percentage, cpt);
            }
        }
    }).then(success).catch(error);
}


const handleTimeoutUpload = (uploadId) => {
    let ossInfo = uploadTaskMap[uploadId];
    if (ossInfo) {
        const ossClient = ossInfo.ossClient;
        handleUploadError({ e: { name: 'timeout' }, callback: ossInfo.callback, uploadId });

        if (ossInfo.timer) {
            clearTimeout(ossInfo.timer);
            ossInfo.timer = null;
        }
    }
}
const handleAliCancelUpload = ({ ossClient, filePath, uploadId })=> {// 取消上传
    if (!ossClient) {
        return;
    }
    const ossInfo = uploadTaskMap[uploadId];
    ossClient.cancel();
    ossInfo.callback && ossInfo.callback("cancel", {name:'cancel'}, uploadId);
    ossClient.listParts(filePath, uploadId, (err, data) => {
        if (err) {
            return console.error('Failed to list parts:', err);
        }
        if (data.parts && data.parts.length > 0) {
            const deleteParts = data.parts.map(part => ({ partNumber: part.partNumber, etag: part.etag }));
            ossClient.abortMultipartUpload(filePath, uploadId, deleteParts, abortErr => {
                if (abortErr) {
                    console.error('Failed to abort multipart upload and delete parts:', abortErr);
                } else {
                    console.log('Multipart upload aborted and parts deleted successfully.');
                }
            });
        }
    });
}
// 获取 OSS 客户端
export async function AliGetOssClient(bucket, fileName) {
    try {
        const data = await getOSSToken(bucket, fileName);
        return new OSS({
            bucket: data.bucket,
            region: data.region,
            accessKeyId: data.accessKeyId,
            accessKeySecret: data.accessKeySecret,
            stsToken: data.stsToken,
            timeout: 60 * 60 * 1000,
            refreshSTSToken: async () => {
                const info = await getOSSToken(bucket, fileName);
                return {
                    accessKeyId: info.accessKeyId,
                    accessKeySecret: info.accessKeySecret,
                    stsToken: info.stsToken
                };
            },
            refreshSTSTokenInterval: new Date(data.expiresAt) - new Date() - 1000 * 60
        });
    } catch (error) {
        throw new Error('获取OSS客户端失败: ' + error.message);
    }
}
// 实际执行暂停上传的函数
const executePauseUpload = (uploadId, skipCallback = false) => {
    const ossInfo = uploadTaskMap[uploadId];
    if (ossInfo && ossInfo.pause) { // 只有在状态为暂停时才执行
        ossInfo.isReloading = false; // 重置重新加载状态

        // 执行实际的暂停动作
        if (ossInfo.ossClient) {
            ossInfo.ossClient.cancel();
        }
        if (ossInfo.timer) {
            clearTimeout(ossInfo.timer);
            ossInfo.timer = null;
        }

        // 更新内部状态
        ossInfo.uploading = false;

        // 如果需要调用 callback（用于首次暂停）
        if (!skipCallback && ossInfo.callback) {
            ossInfo.callback("error", { name: 'pause' }, uploadId);
        }
    }
};

// 防抖执行暂停上传（用于频繁操作）
const debouncedPauseUpload = Tool.debounce((uploadId) => executePauseUpload(uploadId, true), 1000, true);

// 暂停上传 - 智能处理首次暂停和频繁操作
export const AliPauseUpload = function(uploadId) {
    const ossInfo = uploadTaskMap[uploadId];
    if (!ossInfo) {
        return;
    }

    const now = Date.now();
    const lastOperationTime = Math.max(ossInfo.lastPauseTime || 0, ossInfo.lastResumeTime || 0);
    const isFrequentOperation = (now - lastOperationTime) < 2000; // 2秒内的操作视为频繁操作

    // 立即修改状态和UI
    ossInfo.pause = true;
    ossInfo.lastPauseTime = now;

    // 立即更新UI状态
    if (ossInfo.callback) {
        ossInfo.callback("error", { name: 'pause' }, uploadId);
    }

    if (isFrequentOperation) {
        // 频繁操作：延迟执行实际动作
        debouncedPauseUpload(uploadId);
    } else {
        // 首次暂停：立即执行实际暂停
        executePauseUpload(uploadId, true); // 跳过callback，因为UI已经更新了
    }
};

// 实际执行恢复上传的函数
const executeResumeUpload = async (uploadId, skipCallback = false) => {
    const ossInfo = uploadTaskMap[uploadId];
    if (!ossInfo) {
        console.log("!ossInfo",uploadId,ossInfo)
        return;
    }

    // 关键：检查最终状态，如果最终状态是暂停，则不执行恢复
    if (ossInfo.pause) {
        console.log("AliResumeUpload final state is paused, skip resume", uploadId, ossInfo)
        return;
    }

    if(ossInfo.isReloading){
        console.log("AliResumeUpload isReloading",uploadId,ossInfo)
        return
    }

    console.log("AliResumeUpload executing resume upload", uploadId)
    ossInfo.isReloading = true
    clearTimeout(ossInfo.timer);
    ossInfo.isTimeout = false;
    ossInfo.timer = setTimeout(() => handleTimeoutUpload(uploadId), ossInfo.timeout);

    try {
        // 如果需要调用 callback（用于首次恢复）
        if (!skipCallback && ossInfo.callback) {
            ossInfo.callback("progress", Math.round(ossInfo.percentage * 100), uploadId);
        }

        const filePathList = ossInfo.copyOssPath ? [ossInfo.copyOssPath, ossInfo.filePath] : ossInfo.filePath;
        const ossClient = await AliGetOssClient(ossInfo.bucketName, filePathList);
        ossInfo.ossClient = ossClient;

        handleReUploadFile({ ossClient, file: ossInfo.checkPoint.file, filePath: ossInfo.filePath, checkPoint: ossInfo.checkPoint }, {
            progress: (percentage, cpt) => {
                handleProgress({ percentage, cpt, callback: ossInfo.callback, ossClient, uploadId })
            },
            success: (res) =>{
                ossInfo.isReloading = false
                handleUploadSuccess(res, ossInfo.callback, uploadId)
            } ,
            error: (e) =>{
                ossInfo.isReloading = false
                handleUploadError({ e, callback: ossInfo.callback, uploadId })
            }
        });

    } catch (error) {
        console.error(error);
        ossInfo.isReloading = false
    }
};

// 防抖执行恢复上传（用于频繁操作）
const debouncedResumeUpload = Tool.debounce((uploadId) => executeResumeUpload(uploadId, true), 1000, true);

// 恢复上传 - 永远延迟执行，以最终状态为准
export const AliResumeUpload = function({ uploadId }) {
    const ossInfo = uploadTaskMap[uploadId];
    if (!ossInfo) {
        console.log("!ossInfo",uploadId,ossInfo)
        return;
    }

    // 立即修改状态和UI
    ossInfo.pause = false;
    ossInfo.lastResumeTime = Date.now();

    // 立即更新UI状态，让用户看到恢复状态
    if (ossInfo.callback) {
        ossInfo.callback("progress", Math.round(ossInfo.percentage * 100), uploadId);
    }

    // 重新上传永远延迟执行
    debouncedResumeUpload(uploadId);
};

// 文件上传
export function AliUploadFile({ bucket = '', file = null, filePath = '', callback = null, timeout = 30000, copyOssPath = '' } = {}) {
    return new Promise(async (resolve, reject) => {
        try {
            // 只加载 OSS
            const OSS = await ensureOSS();

            const systemConfig = window.vm.$store.state.systemConfig;
            const bucketName = bucket || systemConfig.serverInfo.oss_attachment_server.bucket;
            let ossClient = null
            let filePathList = copyOssPath ? [copyOssPath,filePath] :filePath
            try {
                ossClient = await AliGetOssClient(bucketName, filePathList);
            } catch (error) {
                console.error(error,'AliGetOssClient');
                let errorName = {name:'tokenError'}
                if(copyOssPath){
                    errorName = {name:'timeOut'}
                }
                callback && callback("error", errorName);
                return
            }
            let uploadId = '';

            handleUploadOrCopy({
                ossClient, file, filePath, copyOssPath,
                progress: async (percentage, cpt) => {
                    if (cpt && cpt.uploadId && uploadTaskMap[cpt.uploadId] && uploadTaskMap[cpt.uploadId].isResumeUploading) {
                        return
                    }
                    if(cpt&&cpt.uploadId){
                        uploadId = cpt.uploadId
                        if(uploadTaskMap[cpt.uploadId]){
                            //如何之前是超时的，则需要检测一次当前网络状态
                            if(uploadTaskMap[cpt.uploadId].isTimeout){
                                if(Tool.checkAppClient('App')){
                                    try {
                                        const networkType = await Tool.checkNetworkType()
                                        if(networkType !== 'wifi'){
                                            AliPauseUpload(uploadId)
                                            return
                                        }
                                    } catch (error) {
                                        console.error('error')
                                    }

                                }

                            }
                        }
                    }
                    handleProgress({ percentage, cpt, callback, ossClient, uploadId });
                    if(cpt&&cpt.uploadId){
                        uploadId = cpt.uploadId
                        if(uploadTaskMap[cpt.uploadId]){
                            uploadTaskMap[cpt.uploadId].timeout = timeout
                            uploadTaskMap[cpt.uploadId].bucketName = bucketName
                            uploadTaskMap[cpt.uploadId].filePath = filePath
                            uploadTaskMap[cpt.uploadId].copyOssPath = copyOssPath
                            uploadTaskMap[cpt.uploadId].pause = false
                        }
                        if(uploadTaskMap[cpt.uploadId].timer){
                            clearTimeout(uploadTaskMap[cpt.uploadId].timer)
                            uploadTaskMap[cpt.uploadId].timer = null
                            uploadTaskMap[cpt.uploadId].isTimeout = false
                        }
                        uploadTaskMap[cpt.uploadId].timer = setTimeout(() => {
                            uploadTaskMap[cpt.uploadId].isTimeout = true
                            handleTimeoutUpload(uploadId)
                        }, timeout);
                    }
                },
                success: (res) => handleUploadSuccess(res, callback, uploadId),
                error: (e) => handleUploadError({ e, callback, uploadId })
            });

            resolve(ossClient);
        } catch (error) {
            if (callback) {
                callback("error", { name: 'tokenError' });
            }
            reject(error);
        }
    });
}
export function AliCancelUpload(uploadId){
    // 取消上传
    let ossInfo= uploadTaskMap[uploadId]

    if(ossInfo){
        let filePath = ossInfo.filePath
        const ossClient = ossInfo.ossClient
        handleAliCancelUpload({ossClient,filePath,uploadId})
        if(ossInfo.timer){
            clearTimeout(ossInfo.timer)
            ossInfo.timer = null
        }
        delete uploadTaskMap[uploadId]
    }
}
export function AliIsFileExist  ({
    ossClient = null,
    bucket = '',
    filePath='',
}={}) {
    return new Promise(async (resolve,reject)=>{
        try {
            let client = ossClient
            if(!client){
                const systemConfig = window.vm.$store.state.systemConfig;
                const bucketName = bucket || systemConfig.serverInfo.oss_attachment_server.bucket;
                client = await AliGetOssClient(bucketName, filePath);
            }

            await client.head(filePath,{});
            resolve(true);
            console.log('isFileExist',true)
        } catch (error) {
            resolve(false);
            console.log('isFileExist',false,error)
        }
    })
};
